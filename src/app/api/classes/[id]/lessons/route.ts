import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuth } from '@/lib/auth';

const prisma = new PrismaClient();

// GET /api/classes/[id]/lessons - Get lessons for a specific class
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const lessons = await prisma.lesson.findMany({
      where: { classId: id },
      select: {
        id: true,
        title: true,
        lessonNumber: true,
        description: true,
        scheduledDate: true,
        duration: true,
        location: true,
        isCompleted: true,
        notes: true,
        _count: {
          select: {
            attendance: true
          }
        }
      },
      orderBy: { scheduledDate: 'asc' }
    });

    return NextResponse.json(lessons);
  } catch (error) {
    console.error('Get class lessons error:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}