"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Clock, Plus, Calendar, Users, RefreshCw, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';

interface Lesson {
  id: string;
  title: string;
  lessonNumber?: number;
  scheduledDate: string;
  duration: number;
  status: 'scheduled' | 'completed' | 'cancelled';
  attendanceCount: number;
  totalStudents: number;
}

interface ClassLessonsPanelProps {
  classId: string;
  courseId: string;
  numberOfLessons?: number;
  className?: string;
}

export function ClassLessonsPanel({ classId, courseId, numberOfLessons, className }: ClassLessonsPanelProps) {
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog();

  useEffect(() => {
    if (classId) {
      fetchLessons();
    }
  }, [classId]);

  const fetchLessons = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/classes/${classId}/lessons`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch lessons');
      }

      const data = await response.json();
      setLessons(data);
    } catch (err) {
      toast.error('Erro ao carregar aulas');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateLessons = async (force: boolean = false) => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        toast.error('Sessão expirada. Faça login novamente.');
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/classes/${classId}/generate-lessons`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ force })
      });

      if (!response.ok) {
        const error = await response.json();

        // If lessons already exist and force is false, show confirmation dialog
        if (error.error?.includes('já possui aulas') && !force) {
          showConfirmation({
            title: 'Regenerar Aulas',
            description: `Esta turma já possui ${error.existingLessons || lessons.length} aulas. Deseja regenerar todas as aulas? Esta ação irá substituir as aulas existentes.`,
            confirmText: 'Regenerar Aulas',
            cancelText: 'Cancelar',
            variant: 'default',
            icon: 'warning',
            onConfirm: () => handleGenerateLessons(true)
          });
          return;
        }

        throw new Error(error.error || 'Falha ao gerar aulas');
      }

      const result = await response.json();
      toast.success(result.message || 'Aulas geradas com sucesso!');
      fetchLessons();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao gerar aulas');
    }
  };

  const handleViewLesson = (lessonId: string) => {
    router.push(`/admin/attendance/${lessonId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'scheduled': return 'Agendada';
      case 'completed': return 'Concluída';
      case 'cancelled': return 'Cancelada';
      default: return status;
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg p-4 shadow-sm border ${className}`}>
        <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
          <Clock className="w-5 h-5 mr-2 text-[#667eea]" />
          Aulas da Turma
        </h4>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-5 h-5 animate-spin mr-2 text-[#667eea]" />
          <span className="text-gray-600">Carregando aulas...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg p-4 shadow-sm border ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h4 className="font-semibold text-gray-900 flex items-center">
          <Clock className="w-5 h-5 mr-2 text-[#667eea]" />
          Aulas ({lessons.length})
        </h4>
        {numberOfLessons && numberOfLessons > 0 && (
          <Button onClick={() => handleGenerateLessons()} size="sm">
            {lessons.length > 0 ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                Gerar Aulas Novamente
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Gerar Aulas
              </>
            )}
          </Button>
        )}
      </div>

      {lessons.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <Clock className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p className="font-medium">Nenhuma aula programada</p>
          <p className="text-sm">
            {numberOfLessons && numberOfLessons > 0
              ? "Clique em 'Gerar Aulas' para criar as aulas automaticamente."
              : "Configure o número de aulas no curso para gerar as aulas."
            }
          </p>
        </div>
      ) : (
        <div className="space-y-3 max-h-80 overflow-y-auto">
          {lessons.map((lesson) => {
            const isPast = new Date(lesson.scheduledDate) < new Date();
            const isCurrentLesson = (() => {
              const now = new Date();
              const lessonDate = new Date(lesson.scheduledDate);

              // Check if this is the next upcoming lesson or current lesson
              const upcomingLessons = lessons.filter(l => new Date(l.scheduledDate) >= now);

              if (upcomingLessons.length > 0) {
                return lesson.id === upcomingLessons[0].id;
              } else {
                // If no upcoming lessons, highlight the last lesson
                return lesson.id === lessons[lessons.length - 1]?.id;
              }
            })();

            return (
              <div
                key={lesson.id}
                className={`bg-gray-50 p-3 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer ${
                  isCurrentLesson ? 'ring-2 ring-[#667eea] bg-blue-50' : ''
                }`}
                onClick={() => handleViewLesson(lesson.id)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className={`font-medium ${isCurrentLesson ? 'text-[#667eea]' : 'text-gray-900'}`}>
                      Aula {lesson.lessonNumber || 1}: {lesson.title}
                      {isCurrentLesson && <span className="ml-2 text-xs font-normal text-[#667eea]">● Atual</span>}
                    </div>
                    {lesson.description && (
                      <div className="text-sm text-gray-600 mt-1">{lesson.description}</div>
                    )}
                  </div>
                  <Badge className={getStatusColor(lesson.status)}>
                    {getStatusText(lesson.status)}
                  </Badge>
                </div>

                <div className="text-xs text-gray-500 space-y-1">
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {formatDate(lesson.scheduledDate)} • {lesson.duration} minutos
                  </div>

                  {isPast && (
                    <div className="flex items-center space-x-4 mt-2 pt-2 border-t border-gray-200">
                      <span className="flex items-center text-green-600">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                        {lesson.attendanceCount} presentes
                      </span>
                      <span className="flex items-center text-gray-600">
                        <Users className="w-3 h-3 mr-1" />
                        {lesson.totalStudents} total
                      </span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
      <ConfirmationDialog />
    </div>
  );
}