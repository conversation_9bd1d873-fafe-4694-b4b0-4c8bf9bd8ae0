"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Users, Plus, X, Mail, Phone, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';

interface Student {
  id: string;
  name: string;
  email: string;
  phone?: string;
  enrollmentDate: string;
  status?: string;
}

interface ClassStudentsPanelProps {
  classId: string;
  className?: string;
}

export function ClassStudentsPanel({ classId, className }: ClassStudentsPanelProps) {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (classId) {
      fetchStudents();
    }
  }, [classId]);

  const fetchStudents = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/classes/${classId}/students`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch students');
      }

      const data = await response.json();
      setStudents(data);
    } catch (err) {
      toast.error('Erro ao carregar alunos');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveStudent = async (studentId: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/classes/${classId}/students/${studentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to remove student');
      }

      toast.success('Aluno removido da turma');
      fetchStudents();
    } catch (err) {
      toast.error('Erro ao remover aluno');
    }
  };

  const handleAddStudents = () => {
    router.push(`/admin/classes/${classId}/enrollments`);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Alunos Matriculados
          </CardTitle>
          <CardDescription>Carregando alunos...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg p-4 shadow-sm border ${className}`}>
        <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
          <Users className="w-5 h-5 mr-2 text-[#667eea]" />
          Alunos Matriculados
        </h4>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-5 h-5 animate-spin mr-2 text-[#667eea]" />
          <span className="text-gray-600">Carregando alunos...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg p-4 shadow-sm border ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h4 className="font-semibold text-gray-900 flex items-center">
          <Users className="w-5 h-5 mr-2 text-[#667eea]" />
          Alunos Matriculados ({students.length})
        </h4>
        <Button onClick={handleAddStudents} size="sm">
          <Plus className="w-4 h-4 mr-2" />
          Adicionar Alunos
        </Button>
      </div>

      {students.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <Users className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p className="font-medium">Nenhum aluno matriculado</p>
          <p className="text-sm">Esta turma ainda não possui alunos.</p>
        </div>
      ) : (
        <div className="space-y-3 max-h-80 overflow-y-auto">
          {students.map((student) => (
            <div key={student.id} className="bg-gray-50 p-3 rounded-lg flex items-center justify-between hover:bg-gray-100 transition-colors">
              <div className="flex-1">
                <div className="font-medium text-gray-900">{student.name}</div>
                <div className="text-sm text-gray-600 flex items-center space-x-4 mt-1">
                  {student.email && (
                    <span className="flex items-center">
                      <Mail className="w-3 h-3 mr-1" />
                      {student.email}
                    </span>
                  )}
                  {student.phone && (
                    <span className="flex items-center">
                      <Phone className="w-3 h-3 mr-1" />
                      {student.phone}
                    </span>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={student.status === 'active' ? 'success' : 'secondary'} className="ml-3">
                  {student.status === 'active' ? 'Ativo' : 'Inativo'}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveStudent(student.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}